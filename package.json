{"name": "pao<PERSON>", "version": "1.0.0", "description": "个人管理软件", "main": "main/index.js", "author": "codec", "homepage": "https://electron-vite.org", "scripts": {"format": "prettier --write .", "lint": "eslint --cache .", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "prisma generate && electron-vite dev", "dev:no-check": "electron-vite dev", "build": "prisma generate && electron-vite build && electron-builder", "build:with-check": "npm run typecheck && prisma generate && electron-vite build", "postinstall": "electron-builder install-app-deps && prisma generate", "build:unpack": "prisma generate && npm run build && electron-builder --dir", "build:win": "prisma generate && npm run build && electron-builder --win", "build:mac": "electron-vite build && electron-builder --mac", "build:linux": "electron-vite build && electron-builder --linux", "check-translations": "node scripts/checkTranslations.js", "i18n": "npm run check-translations"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^4.0.0", "@milkdown/crepe": "^7.15.1", "@milkdown/kit": "^7.15.1", "@milkdown/core": "^7.15.1", "@milkdown/react": "^7.15.1", "@milkdown/theme-nord": "^7.15.1", "@prisma/client": "^6.11.1", "@radix-ui/react-checkbox": "^1.1.7", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "chokidar": "^4.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "electron-updater": "^6.3.9", "lucide-react": "^0.525.0", "marked": "^16.1.0", "marked-gfm-heading-id": "^4.1.2", "marked-highlight": "^2.2.2", "prisma": "^6.11.1", "prismjs": "^1.30.0", "react-router-dom": "^7.6.3", "react-window": "^1.8.8", "tailwind-merge": "^3.3.1", "zustand": "^5.0.6"}, "devDependencies": {"@electron-toolkit/eslint-config-prettier": "^3.0.0", "@electron-toolkit/eslint-config-ts": "^3.0.0", "@electron-toolkit/tsconfig": "^1.0.1", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22.14.1", "@types/react": "^19.1.1", "@types/react-dom": "^19.1.2", "@types/react-window": "^1.8.8", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "electron": "^35.1.5", "electron-builder": "^25.1.8", "electron-vite": "^3.1.0", "eslint": "^9.24.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "postcss": "^8.5.6", "prettier": "^3.5.3", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.3", "vite": "^6.2.6", "vite-plugin-static-copy": "^3.1.1"}, "pnpm": {"onlyBuiltDependencies": ["electron", "esbuild"]}}