// prismaClient.ts

import { PrismaClient } from '@prisma/client' // 这行 import 仅用于 TypeScript 类型检查
import { app } from 'electron'
import path from 'path'

// 动态 require 是必需的，因为我们需要在注入路径后重新加载模块
// eslint-disable-next-line @typescript-eslint/no-require-imports
const Module = require('module')

export function createPrismaClient(): PrismaClient {
  const isDevelopment = !app.isPackaged

  if (isDevelopment) {
    // 开发模式保持不变
    return new PrismaClient()
  } else {
    // --- 生产模式下的终极方案 ---

    // 1. 定义我们通过 extraResources 复制过去的 node_modules 目录的路径
    const resourcesPath = process.resourcesPath
    const customModulesPath = path.join(resourcesPath, 'node_modules')
    // 2. 将这个路径添加到 Node.js 的全局模块搜索路径列表的最前面
    // 这会让 'require('@prisma/client')' 优先在 resources/node_modules 中查找
    if (Module.globalPaths.indexOf(customModulesPath) === -1) {
      Module.globalPaths.unshift(customModulesPath)
    }
    console.log('Injecting custom module path:', customModulesPath)
    console.log('Node.js global search paths:', Module.globalPaths)

    // 3. 现在，标准的 require 应该可以从我们注入的路径中找到模块了
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const { PrismaClient: PrismaClientProd } = require('@prisma/client')

    // 4. 像之前一样，只覆盖数据库文件路径
    const dbPath = path.join(app.getPath('userData'), 'paolife.db')
    return new PrismaClientProd({
      datasources: {
        db: {
          url: `file:${dbPath}`
        }
      }
    })
  }
}
