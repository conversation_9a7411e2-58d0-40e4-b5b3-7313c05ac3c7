import { PrismaClient } from '@prisma/client'
import { app } from 'electron'
import path from 'path'
import fs from 'fs'

/**
 * Database initializer for production environment
 * Creates tables and applies schema when database doesn't exist
 */
export class DatabaseInitializer {
  private prisma: PrismaClient

  constructor(prisma: PrismaClient) {
    this.prisma = prisma
  }

  /**
   * Check if database has been initialized (has tables)
   */
  async isDatabaseInitialized(): Promise<boolean> {
    try {
      // Check for multiple critical tables to ensure database is properly initialized
      const tables = await this.prisma.$queryRaw<Array<{ name: string }>>`
        SELECT name FROM sqlite_master WHERE type='table' AND name IN ('User', 'Project', 'Area', 'Task', 'ProjectKPI')
      `

      // Database is considered initialized if we have at least the core tables
      const requiredTables = ['User', 'Project', 'Area', 'Task']
      const existingTableNames = tables.map(t => t.name)
      const hasRequiredTables = requiredTables.every(table => existingTableNames.includes(table))

      if (hasRequiredTables) {
        console.log('Database is properly initialized with tables:', existingTableNames)
        return true
      } else {
        console.log('Database missing required tables. Found:', existingTableNames, 'Required:', requiredTables)
        return false
      }
    } catch (error) {
      console.log('Database not initialized or connection failed, will create schema:', error)
      return false
    }
  }

  /**
   * Initialize database schema in production
   */
  async initializeSchema(): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('Initializing database schema...')

      // In production, we don't need the schema file - we create tables directly with SQL
      // This is more reliable than trying to find the schema file in the packaged app
      await this.createTables()

      console.log('Database schema initialized successfully')
      return { success: true }

    } catch (error) {
      console.error('Failed to initialize database schema:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Create all tables using raw SQL
   */
  private async createTables(): Promise<void> {
    console.log('Creating database tables...')

    // Create tables in dependency order
    const createTableQueries = [
      // User table
      `CREATE TABLE IF NOT EXISTS "User" (
        "id" TEXT NOT NULL PRIMARY KEY,
        "username" TEXT NOT NULL UNIQUE,
        "password" TEXT NOT NULL,
        "settings" TEXT
      )`,

      // Area table
      `CREATE TABLE IF NOT EXISTS "Area" (
        "id" TEXT NOT NULL PRIMARY KEY,
        "name" TEXT NOT NULL,
        "description" TEXT,
        "color" TEXT,
        "icon" TEXT,
        "archived" BOOLEAN NOT NULL DEFAULT false,
        "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
      )`,

      // Project table
      `CREATE TABLE IF NOT EXISTS "Project" (
        "id" TEXT NOT NULL PRIMARY KEY,
        "name" TEXT NOT NULL,
        "description" TEXT,
        "status" TEXT NOT NULL DEFAULT 'planning',
        "priority" TEXT NOT NULL DEFAULT 'medium',
        "startDate" DATETIME,
        "endDate" DATETIME,
        "archived" BOOLEAN NOT NULL DEFAULT false,
        "areaId" TEXT,
        "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY ("areaId") REFERENCES "Area" ("id") ON DELETE SET NULL ON UPDATE CASCADE
      )`,

      // Task table
      `CREATE TABLE IF NOT EXISTS "Task" (
        "id" TEXT NOT NULL PRIMARY KEY,
        "title" TEXT NOT NULL,
        "description" TEXT,
        "status" TEXT NOT NULL DEFAULT 'todo',
        "priority" TEXT NOT NULL DEFAULT 'medium',
        "dueDate" DATETIME,
        "completedAt" DATETIME,
        "projectId" TEXT,
        "areaId" TEXT,
        "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY ("projectId") REFERENCES "Project" ("id") ON DELETE SET NULL ON UPDATE CASCADE,
        FOREIGN KEY ("areaId") REFERENCES "Area" ("id") ON DELETE SET NULL ON UPDATE CASCADE
      )`,

      // ResourceLink table
      `CREATE TABLE IF NOT EXISTS "ResourceLink" (
        "id" TEXT NOT NULL PRIMARY KEY,
        "title" TEXT NOT NULL,
        "url" TEXT NOT NULL,
        "description" TEXT,
        "type" TEXT NOT NULL DEFAULT 'link',
        "projectId" TEXT,
        "areaId" TEXT,
        "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY ("projectId") REFERENCES "Project" ("id") ON DELETE SET NULL ON UPDATE CASCADE,
        FOREIGN KEY ("areaId") REFERENCES "Area" ("id") ON DELETE SET NULL ON UPDATE CASCADE
      )`,

      // Habit table
      `CREATE TABLE IF NOT EXISTS "Habit" (
        "id" TEXT NOT NULL PRIMARY KEY,
        "name" TEXT NOT NULL,
        "description" TEXT,
        "frequency" TEXT NOT NULL DEFAULT 'daily',
        "targetValue" INTEGER,
        "unit" TEXT,
        "areaId" TEXT,
        "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY ("areaId") REFERENCES "Area" ("id") ON DELETE SET NULL ON UPDATE CASCADE
      )`,

      // HabitRecord table
      `CREATE TABLE IF NOT EXISTS "HabitRecord" (
        "id" TEXT NOT NULL PRIMARY KEY,
        "date" DATETIME NOT NULL,
        "completed" BOOLEAN NOT NULL DEFAULT true,
        "value" REAL,
        "note" TEXT,
        "habitId" TEXT NOT NULL,
        "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY ("habitId") REFERENCES "Habit" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
        UNIQUE("habitId", "date")
      )`,

      // AreaMetric table
      `CREATE TABLE IF NOT EXISTS "AreaMetric" (
        "id" TEXT NOT NULL PRIMARY KEY,
        "name" TEXT NOT NULL,
        "description" TEXT,
        "unit" TEXT,
        "targetValue" REAL,
        "currentValue" REAL NOT NULL DEFAULT 0,
        "trackingType" TEXT NOT NULL DEFAULT 'manual',
        "direction" TEXT NOT NULL DEFAULT 'higher_better',
        "areaId" TEXT NOT NULL,
        "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY ("areaId") REFERENCES "Area" ("id") ON DELETE CASCADE ON UPDATE CASCADE
      )`,

      // AreaMetricRecord table
      `CREATE TABLE IF NOT EXISTS "AreaMetricRecord" (
        "id" TEXT NOT NULL PRIMARY KEY,
        "value" TEXT NOT NULL,
        "note" TEXT,
        "recordedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "metricId" TEXT NOT NULL,
        "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY ("metricId") REFERENCES "AreaMetric" ("id") ON DELETE CASCADE ON UPDATE CASCADE
      )`,

      // ProjectKPI table
      `CREATE TABLE IF NOT EXISTS "ProjectKPI" (
        "id" TEXT NOT NULL PRIMARY KEY,
        "name" TEXT NOT NULL,
        "value" TEXT NOT NULL,
        "target" TEXT,
        "unit" TEXT,
        "frequency" TEXT,
        "direction" TEXT NOT NULL DEFAULT 'increase',
        "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "projectId" TEXT NOT NULL,
        "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY ("projectId") REFERENCES "Project" ("id") ON DELETE CASCADE ON UPDATE CASCADE
      )`,

      // KPIRecord table
      `CREATE TABLE IF NOT EXISTS "KPIRecord" (
        "id" TEXT NOT NULL PRIMARY KEY,
        "value" TEXT NOT NULL,
        "note" TEXT,
        "recordedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "kpiId" TEXT NOT NULL,
        FOREIGN KEY ("kpiId") REFERENCES "ProjectKPI" ("id") ON DELETE CASCADE ON UPDATE CASCADE
      )`,

      // Checklist table
      `CREATE TABLE IF NOT EXISTS "Checklist" (
        "id" TEXT NOT NULL PRIMARY KEY,
        "name" TEXT NOT NULL,
        "template" TEXT NOT NULL,
        "areaId" TEXT NOT NULL,
        "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY ("areaId") REFERENCES "Area" ("id") ON DELETE CASCADE ON UPDATE CASCADE
      )`,

      // ChecklistInstance table
      `CREATE TABLE IF NOT EXISTS "ChecklistInstance" (
        "id" TEXT NOT NULL PRIMARY KEY,
        "status" TEXT NOT NULL,
        "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "completedAt" DATETIME,
        "checklistId" TEXT NOT NULL,
        FOREIGN KEY ("checklistId") REFERENCES "Checklist" ("id") ON DELETE CASCADE ON UPDATE CASCADE
      )`,

      // RecurringTask table
      `CREATE TABLE IF NOT EXISTS "RecurringTask" (
        "id" TEXT NOT NULL PRIMARY KEY,
        "title" TEXT NOT NULL,
        "description" TEXT,
        "frequency" TEXT NOT NULL DEFAULT 'weekly',
        "nextDueDate" DATETIME,
        "lastCompletedAt" DATETIME,
        "areaId" TEXT,
        "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY ("areaId") REFERENCES "Area" ("id") ON DELETE SET NULL ON UPDATE CASCADE
      )`,

      // Create indexes for better performance
      `CREATE INDEX IF NOT EXISTS "Project_areaId_idx" ON "Project"("areaId")`,
      `CREATE INDEX IF NOT EXISTS "Task_projectId_idx" ON "Task"("projectId")`,
      `CREATE INDEX IF NOT EXISTS "Task_areaId_idx" ON "Task"("areaId")`,
      `CREATE INDEX IF NOT EXISTS "ResourceLink_projectId_idx" ON "ResourceLink"("projectId")`,
      `CREATE INDEX IF NOT EXISTS "ResourceLink_areaId_idx" ON "ResourceLink"("areaId")`,
      `CREATE INDEX IF NOT EXISTS "AreaMetric_areaId_idx" ON "AreaMetric"("areaId")`,
      `CREATE INDEX IF NOT EXISTS "Habit_areaId_idx" ON "Habit"("areaId")`,
      `CREATE INDEX IF NOT EXISTS "Checklist_areaId_idx" ON "Checklist"("areaId")`,
      `CREATE INDEX IF NOT EXISTS "RecurringTask_areaId_idx" ON "RecurringTask"("areaId")`,
      `CREATE INDEX IF NOT EXISTS "ProjectKPI_projectId_idx" ON "ProjectKPI"("projectId")`,
      `CREATE INDEX IF NOT EXISTS "KPIRecord_kpiId_recordedAt_idx" ON "KPIRecord"("kpiId", "recordedAt")`,
      `CREATE INDEX IF NOT EXISTS "HabitRecord_habitId_date_idx" ON "HabitRecord"("habitId", "date")`,
      `CREATE INDEX IF NOT EXISTS "AreaMetricRecord_metricId_recordedAt_idx" ON "AreaMetricRecord"("metricId", "recordedAt")`,
      `CREATE INDEX IF NOT EXISTS "ChecklistInstance_checklistId_idx" ON "ChecklistInstance"("checklistId")`
    ]

    // Execute each query
    for (const query of createTableQueries) {
      try {
        await this.prisma.$executeRawUnsafe(query)
        console.log('✓ Table created successfully')
      } catch (error) {
        console.error('Failed to create table:', error)
        throw error
      }
    }

    console.log('All tables created successfully')
  }

  /**
   * Verify database integrity
   */
  async verifyDatabase(): Promise<{ success: boolean; tables: string[]; error?: string }> {
    try {
      const tables = await this.prisma.$queryRaw<Array<{ name: string }>>`
        SELECT name FROM sqlite_master WHERE type='table' ORDER BY name
      `

      const tableNames = tables.map(t => t.name).filter(name => !name.startsWith('sqlite_'))

      console.log('Database tables found:', tableNames)

      return {
        success: true,
        tables: tableNames
      }
    } catch (error) {
      return {
        success: false,
        tables: [],
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }
}
