import { resolve } from 'path'
import { defineConfig, externalizeDepsPlugin } from 'electron-vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  main: {
    entry: resolve(__dirname, 'src/main/index.ts'),
    build: {
      external: ['@prisma/client']
    },
    plugins: [externalizeDepsPlugin()]
  },
  preload: {
    plugins: [externalizeDepsPlugin()]
  },
  renderer: {
    resolve: {
      alias: {
        '@renderer': resolve('src/renderer/src')
      }
    },
    plugins: [react()],
    server: {
      fs: {
        // 允许访问 node_modules 中的文件（用于开发模式的本地资源）
        allow: ['..']
      }
    },
    assetsInclude: ['**/*.wasm'] // Include WebAssembly files if needed
  }
})
